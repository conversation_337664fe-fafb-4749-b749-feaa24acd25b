﻿using Crystalaligner.Application.Commands.Orders;
using Crystalaligner.Application.Queries.MultipleQuery.Orders;
using Crystalaligner.Application.Queries.MultipleQuery.Packages;
using Crystalaligner.Application.Queries.MultipleQuery.Users;
using Crystalaligner.Application.Queries.SingleQuery.Orders;
using Crystalaligner.Core.Base.Controller;
using Crystalaligner.Core.Base.Helpers.Pagination;
using Crystalaligner.Core.Base.Responses.ApiResponses;
using Crystalaligner.Core.Extensions;
using Crystalaligner.Core.Extensions.FilterExtensions;
using Crystalaligner.Management.Domain.Entities.Orders;
using Crystalaligner.Management.Domain.Entities.Users;
using Crystalaligner.Model.Responses.Orders;
using Crystalaligner.Tools;
using FluentFTP;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.JsonPatch;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using static Crystalaligner.Helper.Enumerations;

namespace Crystalaligner.Controllers.v1.Orders;

[Route("api/v{version:apiVersion}/[controller]")]
[ApiVersion("1.0")]
[ApiController, Authorize]
public class RefinementsController : BaseController
{
    public static IConfiguration _configuration { get; set; }
    private readonly IMediator _mediatr;
    public RefinementsController(IMediator mediatr, IConfiguration configuration)
    {
        _mediatr = mediatr;
        _configuration = configuration;
    }


    [HttpGet("GetExcelFileAsync")]
    public async Task<IActionResult> GetExcelFileAsync([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<Refinement>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        request.IncludePaths = includes => includes.Include(a => a.Patient);

        var packageTypeQuery = new PackageTypesQuery();
        var resultpackageType = await _mediatr.Send(packageTypeQuery);

        Expression<Func<UserAddress, bool>> predicate = s => s.UserId > 0;
        var includes = new string[] { "City", "District" };
        var userAdressQuery = new UserAddressesQuery(predicate, includes);
        var resultuserAdress = await _mediatr.Send(userAdressQuery);


        var query = new RefinementsPagedQuery(request);
        var result = await _mediatr.Send(query);
        if (result == null) return NotFound();
        var excelFile = CreateExcelFile(result.Data);
        List<OrderExcelResponse> response = new List<OrderExcelResponse>();
        foreach (var item in result.Data)
        {
            OrderExcelResponse order = new OrderExcelResponse();

            var adres = resultuserAdress.Data.Where(a => a.UserId == item.CreatedId).FirstOrDefault();

            order.Durum = item.Status.ToString();
            order.Hasta = item.Patient.Name;
            order.Paket = resultpackageType.Data.Where(s => s.Id == item.PackageTypeId).FirstOrDefault()?.Name;
            order.KayitTarihi = item.CreatedDate.Value.ToString("dd.MM.yyy");
            order.DoktorAdi = item.CreatedBy;
            order.DoktorAdres = adres?.Address + " " + adres?.District.Name + " / " + adres?.City.Name;
            response.Add(order);
        }


        return File(excelFile, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "EkPlak_Listesi.xlsx");
    }

    [HttpGet]
    public async Task<IActionResult> Get()
    {
        var includes = new string[] { };

        Expression<Func<Refinement, bool>> predicate = s => s.Id > 0;

        var query = new RefinementsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetByAgentId")]
    public async Task<IActionResult> GetByAgentId(int agentId)
    {
        var includes = new string[] { "User" };

        Expression<Func<Refinement, bool>> predicate = s => s.User.AgentId == agentId;

        var query = new RefinementsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetByDoctorId")]
    public async Task<IActionResult> GetByDoctorId(int doctorId)
    {
        var includes = new string[] { "User" };

        Expression<Func<Refinement, bool>> predicate = s => s.DoctorId == doctorId;

        var query = new RefinementsQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> Get(int id)
    {
        Expression<Func<Refinement, bool>> predicate = s => s.Id == id;

        var includes = new string[] { };

        var query = new RefinementQuery(predicate, includes);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpGet("GetPaged")]
    public async Task<IActionResult> GetPaged([FromQuery] PaginationFilterQuery filterQuery)
    {
        var request = new PagedFilterRequest<Refinement>(filterQuery);
        if (filterQuery.Filters != null)
        {
            request.Predicate = request.Predicate.Filter(request.FilterQuery);
            request.Predicate = request.Predicate.And(x => !x.IsDelete);
        }
        request.OrderBy = request.OrderBy.Sort(request.FilterQuery, a => a.OrderByDescending(s => s.CreatedDate));
        var query = new RefinementsPagedQuery(request);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPost("Post")]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> Post(
    IFormFile fileFaceFront,
    IFormFile fileFaceRight,
    IFormFile fileFaceSmile,
    IFormFile fileTeethFront,
    IFormFile fileTeethRight,
    IFormFile fileTeethLeft,
    IFormFile fileTeethTop,
    IFormFile fileTeethDown,
    IFormFile filePanoramic,
    IFormFile fileStlUpper,
    IFormFile fileStlLower,
    IFormFile fileStlBite,
    [FromForm] int patientId,
    [FromForm] int orderId,
    [FromForm] int doctorId,
    [FromForm] string description,
    [FromForm] int status,
    [FromForm] int ark,
    [FromForm] int antero,
    [FromForm] int packageTypeId,
    [FromForm] int implementedPlanTypeId,
    [FromForm] string specialInstruction,
    [FromForm] string diestema,
    [FromForm] string extraction)
    {

        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.RefinementsUrl;

        var command = new RefinementCreate();

        List<IFormFile> files = new List<IFormFile>()
        {
            fileFaceFront,
            fileFaceRight,
            fileFaceSmile,
            fileTeethFront,
            fileTeethRight,
            fileTeethLeft,
            fileTeethTop,
            fileTeethDown,
            filePanoramic,
            fileStlUpper,
            fileStlLower,
            fileStlBite
        };


        var client = new FtpClient(ftpServer, username, password, 21);
        client.Connect();

        if (!client.DirectoryExists(baseUrl[1..] + patientId))
            client.CreateDirectory(baseUrl + patientId);

        foreach (var file in files)
        {
            if (file is not null && file.Length > 0)
            {
                var newName = file.Name.Replace("file", "");
                string newFileName = patientId.ToString() + "-refinement-" + newName + Path.GetExtension(file.FileName);

                string tempFilePath = Path.GetTempFileName();

                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }

                //string newFileName = "newFileName.txt"; // Yeni dosya adı

                //string tempFilePath = Path.GetTempFileName();

                //using (var stream = new FileStream(tempFilePath, FileMode.Create))
                //{
                //    file.CopyTo(stream);
                //}

                //string tempFilePath = Path.GetTempFileName();

                //using (var stream = new FileStream(tempFilePath, FileMode.Create))
                //{
                //    file.CopyTo(stream);
                //}

                if (!client.FileExists($"{baseUrl}/{patientId}/{file.FileName}"))
                {
                    using var fileStream = System.IO.File.OpenRead(tempFilePath);
                    client.UploadFile(tempFilePath, $"{baseUrl}/{patientId}/{newFileName}");
                }

                // remove file to filename
                string propertyName = file.Name.Replace("file", "").Replace(" ", "");

                // find matched prop.
                var property = command.GetType().GetProperty(propertyName);
                property?.SetValue(command, $"{baseUrl[1..]}{patientId}/{newFileName}");
            }
        }

        client.Disconnect();

        command.Description = description;
        command.Status = (RefinementStatus)status;
        command.PatientId = patientId;
        command.OrderId = orderId;
        command.DoctorId = doctorId;
        command.Ark = (Ark)ark;
        command.Antero = (Antero)antero;
        command.PackageTypeId = packageTypeId;
        command.ImplementedPlanTypeId = implementedPlanTypeId;
        command.SpecialInstruction = specialInstruction;
        command.Diestema = diestema;
        command.Extraction = extraction;

        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }


    [HttpPut]
    [Consumes("multipart/form-data")]
    public async Task<IActionResult> Put(
    IFormFile fileFaceFront,
    IFormFile fileFaceRight,
    IFormFile fileFaceSmile,
    IFormFile fileTeethFront,
    IFormFile fileTeethRight,
    IFormFile fileTeethLeft,
    IFormFile fileTeethTop,
    IFormFile fileTeethDown,
    IFormFile filePanoramic,
    IFormFile fileStlUpper,
    IFormFile fileStlLower,
    IFormFile fileStlBite,
    [FromForm] int id,
    [FromForm] int patientId,
    [FromForm] int orderId,
    [FromForm] int doctorId,
    [FromForm] string description,
    [FromForm] int status,
    [FromForm] int ark,
    [FromForm] int antero,
    [FromForm] int packageTypeId,
    [FromForm] int implementedPlanTypeId,
    [FromForm] string specialInstruction,
    [FromForm] string diestema,
    [FromForm] string extraction)
    {

        var cdnConfig = _configuration.GetSection("Cdn").Get<Cdn>();
        string ftpServer = cdnConfig.FtpServer;
        string username = cdnConfig.Username;
        string password = cdnConfig.Password;
        string baseUrl = cdnConfig.RefinementsUrl;

        var command = new RefinementUpdate();

        List<IFormFile> files = new List<IFormFile>()
        {
            fileFaceFront,
            fileFaceRight,
            fileFaceSmile,
            fileTeethFront,
            fileTeethRight,
            fileTeethLeft,
            fileTeethTop,
            fileTeethDown,
            filePanoramic,
            fileStlUpper,
            fileStlLower,
            fileStlBite
        };


        var client = new FtpClient(ftpServer, username, password, 21);
        client.Connect();

        if (!client.DirectoryExists(baseUrl[1..] + patientId))
            client.CreateDirectory(baseUrl + patientId);

        foreach (var file in files)
        {
            if (file is not null && file.Length > 0)
            {
                var newName = file.Name.Replace("file", "");
                string newFileName = patientId.ToString() + "-refinement-" + newName + Path.GetExtension(file.FileName);

                string tempFilePath = Path.GetTempFileName();

                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    file.CopyTo(stream);
                }

                //string newFileName = "newFileName.txt"; // Yeni dosya adı

                //string tempFilePath = Path.GetTempFileName();

                //using (var stream = new FileStream(tempFilePath, FileMode.Create))
                //{
                //    file.CopyTo(stream);
                //}

                //string tempFilePath = Path.GetTempFileName();

                //using (var stream = new FileStream(tempFilePath, FileMode.Create))
                //{
                //    file.CopyTo(stream);
                //}

                if (!client.FileExists($"{baseUrl}/{patientId}/{file.FileName}"))
                {
                    using var fileStream = System.IO.File.OpenRead(tempFilePath);
                    client.UploadFile(tempFilePath, $"{baseUrl}/{patientId}/{newFileName}");
                }

                // remove file to filename
                string propertyName = file.Name.Replace("file", "").Replace(" ", "");

                // find matched prop.
                var property = command.GetType().GetProperty(propertyName);
                property?.SetValue(command, $"{baseUrl[1..]}{patientId}/{newFileName}");
            }
        }

        client.Disconnect();

        command.Id = id;
        command.Description = description;
        command.Status = (RefinementStatus)status;
        command.PatientId = patientId;
        command.OrderId = orderId;
        command.DoctorId = doctorId;
        command.Ark = (Ark)ark;
        command.Antero = (Antero)antero;
        command.PackageTypeId = packageTypeId;
        command.ImplementedPlanTypeId = implementedPlanTypeId;
        command.SpecialInstruction = specialInstruction;
        command.Diestema = diestema;
        command.Extraction = extraction;

        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPatch]
    public async Task<IActionResult> Patch(int id, [FromBody] JsonPatchDocument<Refinement> JsonPatchDocument)
    {
        var command = new RefinementPatch(id, JsonPatchDocument);

        var result = (ApiResponse)await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut("PutStatus")]
    public async Task<IActionResult> PutStatus([FromBody] RefinementStatusUpdate command)
    {
        var result = await _mediatr.Send(command);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(int id)
    {
        Expression<Func<Refinement, bool>> predicate = s => s.Id == id;

        var query = new RefinementDelete(predicate);

        var result = await _mediatr.Send(query);

        if (result == null) return NotFound();

        return result.Success ? Success(result) : BadRequest(result);
    }

    [HttpPut("PutRefinementShipped")]
    public async Task<IActionResult> PutRefinementStatusToShipper([FromBody] RefinementShippedUpdate command)
    {
        var result = await _mediatr.Send(command);
        if (result == null) return NotFound();
        if (result.Success)
        {
            // var mailSettings = _configuration
            //             .GetSection("MailSettings")
            //             .Get<EmailSenderModel>();
            // string mail = mailSettings.Mail;
            // string password = mailSettings.Password;
            // string host = mailSettings.Host;
            // int port = mailSettings.Port;

            // var builder = new StringBuilder();

            // builder.AppendLine("<!DOCTYPE html>");
            // builder.AppendLine("<html>");
            // builder.AppendLine("<head>");
            // builder.AppendLine("    <style>");
            // builder.AppendLine("        body {");
            // builder.AppendLine("            font-family: Arial, sans-serif;");
            // builder.AppendLine("            line-height: 1.6;");
            // builder.AppendLine("            color: #333333;");
            // builder.AppendLine("            background-color: #f4f4f4;");
            // builder.AppendLine("            margin: 0;");
            // builder.AppendLine("            padding: 0;");
            // builder.AppendLine("        }");
            // builder.AppendLine("        .container {");
            // builder.AppendLine("            max-width: 600px;");
            // builder.AppendLine("            margin: 0 auto;");
            // builder.AppendLine("            background: #ffffff;");
            // builder.AppendLine("            padding: 20px;");
            // builder.AppendLine("        }");
            // builder.AppendLine("        .header {");
            // builder.AppendLine("            background: #333333;");
            // builder.AppendLine("            color: #ffffff;");
            // builder.AppendLine("            padding: 10px 0;");
            // builder.AppendLine("            text-align: center;");
            // builder.AppendLine("        }");
            // builder.AppendLine("        .footer {");
            // builder.AppendLine("            background: #333333;");
            // builder.AppendLine("            color: #ffffff;");
            // builder.AppendLine("            text-align: center;");
            // builder.AppendLine("            padding: 10px 0;");
            // builder.AppendLine("            font-size: 0.8em;");
            // builder.AppendLine("        }");
            // builder.AppendLine("        .content {");
            // builder.AppendLine("            padding: 20px 0;");
            // builder.AppendLine("        }");
            // builder.AppendLine("    </style>");
            // builder.AppendLine("</head>");
            // builder.AppendLine("<body>");
            // builder.AppendLine("    <div class='container'>");
            // builder.AppendLine("        <div class='header'>");
            // builder.AppendLine("            Crystal Aligner");
            // builder.AppendLine("        </div>");
            // builder.AppendLine("");
            // builder.AppendLine("        <div class='content'>");
            // builder.AppendLine("            <h2>Bilgilendirme</h2>");
            // builder.AppendLine($"            <p>Merhaba {result.Data.User.Name} ,</p>");
            // builder.AppendLine($"            <p>Bu e-posta, hastanız {result.Data.Patient.Name} için hazırlanmış plakların kargo firmasına teslim edilmesi konusunda bilgilendirmek amacıyla gönderilmiştir. {result.Data.Shipping.Name} firması ile {result.Data.TrackNumber} takip numarası ile {result.Data.ShippedDate.Value.ToShortDateString()} tarihinde gönderilmiştir.</p>");
            // builder.AppendLine("");
            // builder.AppendLine("");
            //// builder.AppendLine("            <p>[Ekstra bilgi veya talimatlar]</p>");
            // builder.AppendLine("");
            // builder.AppendLine("            <p>Saygılarımızla,<br>");
            // builder.AppendLine("            Crystal Aligner</p>");
            // builder.AppendLine("        </div>");
            // builder.AppendLine("");
            // builder.AppendLine("        <div class='footer'>");
            // builder.AppendLine("            Bu e-posta, bilgilendirme amaçlıdır ve içerdiği bilgiler gizli olabilir. Crystal Aligner © " + DateTime.Now.Year);
            // builder.AppendLine("        </div>");
            // builder.AppendLine("    </div>");
            // builder.AppendLine("</body>");
            // builder.AppendLine("</html>");

            // EmailSenderModel sender = new()
            // {
            //     Mail = mail,
            //     Password = password,
            //     Host = host,
            //     Port = port,
            //     Body = builder.ToString(),
            //     Subject = "Hastanız için hazırlanan plaklar kargoya verildi.",
            //     To = result.Data.User.Email
            // };

            // EmailSender send = new EmailSender();
            // await send.SendEmailAsync(sender);
            return Success(result);
        }
        return BadRequest(result);
    }
}

