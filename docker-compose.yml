version: '3.8'
services:
  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: crystalaligner-db
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=************
    ports:
      - "1434:1433" # Host'ta başka bir port kullana<PERSON>irsin, zorunlu değil
    volumes:
      - mssql_data:/var/opt/mssql
    restart: unless-stopped

  api:
    image: buraxtaa/crystalback:latest
    container_name: crystalaligner-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=db;Database=CrystalAligner_Prod;User=sa;Password=************;TrustServerCertificate=True;
    ports:
      - "127.0.0.1:5001:5000" # Host:5001 -> Container:5000
    depends_on:
      - db
    restart: unless-stopped

  frontend:
    image: buraxtaa/crystaldoctor:latest
    container_name: crystalaligner-frontend
    ports:
      - "80:80" # Host:80 -> Container:80
    depends_on:
      - api
    restart: unless-stopped

volumes:
  mssql_data:
