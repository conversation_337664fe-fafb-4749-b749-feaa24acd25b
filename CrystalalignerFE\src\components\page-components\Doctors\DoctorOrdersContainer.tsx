/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import DetailShadowContainer from '@/components/shared/DetailShadowContainer'
import TableGrid from '@/components/shared/TableGrid'
import { Tooltip, Select } from '@/components/ui'
import Pagination from '@/components/ui/Pagination'
import {
    ORDER_STATUS,
    ORDER_STATUS_COLOR,
} from '@/components/ui/utils/constants'
import { apiGetOrdersByDoctorId } from '@/services/OrderService'
import { useAppSelector } from '@/store'
import { capitalize } from '@/utils/capitalizeFilter'
import { useCallback, useEffect, useState } from 'react'
import { CgExternal, CgInfo } from 'react-icons/cg'
import { Link } from 'react-router-dom'

const DoctorOrdersContainer = ({ doctor }: { doctor: any }) => {
    const [orders, setOrders] = useState<any[]>([])

    const [totalData, setTotalData] = useState<number>(0)
    const [pageSize, setPageSize] = useState<number>(100)
    const [pageIndex, setPageIndex] = useState<number>(0)

    const refreshTable = useAppSelector((state) => state.table.refreshTable)
    const { filterValue, sortField, sortOperator } = useAppSelector(
        (state) => state.filter
    )

    const GetOrders = useCallback(async () => {

        if (!doctor?.Id) {
            console.log('GetOrders - No doctor ID, returning early')
            setOrders([])
            setTotalData(0)
            return
        }

        try {
            const apiParams = {
                pageSize: pageSize,
                page: pageIndex + 1,
                columnField: 'CreatedId',
                filterValue: doctor.Id,
                filterOperator: 'equals',
                sortOperator: sortOperator,
                sortField: sortField && capitalize(sortField),
            }


            const response = await apiGetOrdersByDoctorId(apiParams)


            if ((response as any)?.data?.Success && (response as any)?.data.Data) {
                const dataArray = (response as any).data.Data
                const totalRecords = (response as any).data.TotalRecords
                setOrders(dataArray)
                setTotalData(totalRecords)
            } else {
                setOrders([])
                setTotalData(0)
            }
        } catch (error) {
            console.error('Error fetching doctor orders:', error)
            setOrders([])
            setTotalData(0)
        }
    }, [
        pageSize,
        pageIndex,
        sortField,
        sortOperator,
        refreshTable,
        doctor,
    ])

    useEffect(() => {
        GetOrders()
    }, [GetOrders, refreshTable, doctor])

    const handlePaginationChange = (page: number) => {
        setPageIndex(page - 1)
    }

    const handlePageSizeChange = (size: number) => {
        setPageSize(size)
        setPageIndex(0) // Reset to first page when page size changes
    }

    /*     const [patientList, setPatientList] = useState<any[]>([])

    const GetPatient = useCallback(async (id: number) => {
        try {
            const response = await apiGetPatientById(id)
            if ((response as any)?.data.Data) {
                const dataArray = (response as any).data.Data
                setPatientList((prev) => [...prev, dataArray])
            }
        } catch (e) {
            console.log(e)
        }
    }, [])

    useEffect(() => {
        if (orders.length > 0) {
            orders.map((order: any) => {
                GetPatient(order.PatientId)
            })
        }
    }, [orders, GetPatient]) */

    const tableData = useCallback((): any[] => {
        const arr = []
        for (let i = 0; i < orders?.length; i++) {
            arr.push({
                PatientId: orders[i].PatientId,
                Patient: orders[i].Patient,
                Description: orders[i].Description,
                Status: orders[i].Status,
                CreatedDate: orders[i].CreatedDate,
                CreatedBy: orders[i].CreatedBy,
                UpdatedDate: orders[i].UpdatedDate,
                UpdatedBy: orders[i].UpdatedBy,
                Id: orders[i].Id,
            })
        }
        return arr
    }, [orders])

    const [data, setData] = useState<any[]>(tableData())

    useEffect(() => {
        setData(tableData())
    }, [orders, setData, tableData])

    const DoctorOrdersColumns = [
        {
            header: 'Durum',
            accessorKey: 'Status',
            cell: (id: any) => {
                return (
                    <div className="flex items-center">
                        <div className="flex flex-col ml-2">
                            <span
                                className={`font-semibold text-sm px-3 py-1 rounded-2xl whitespace-nowrap text-white bg-${
                                    ORDER_STATUS_COLOR[
                                        id.row.original.Status
                                    ] || 'blue'
                                }-500`}
                            >
                                {id.row.original.Status == -1
                                    ? 'İyileştirme Talebi'
                                    : ORDER_STATUS[id.row.original.Status]}
                            </span>
                        </div>
                    </div>
                )
            },
        },
        {
            header: 'Hasta',
            accessorKey: 'Patient',
            cell: (id: any) => {
                const patientName = id.row.original.Patient?.Name
                return (
                    <Tooltip title={patientName} className="text-sm">
                        <div className="font-semibold text-sm whitespace-nowrap">
                            {patientName?.length > 15 ? (
                                <span>{patientName?.slice(0, 15)}...</span>
                            ) : (
                                patientName
                            )}
                        </div>
                    </Tooltip>
                )
            },
        },
        /*     {
            header: 'Açıklama',
            accessorKey: 'Description',
            cell: (id: any) => {
                return (
                    <div className="flex items-center">
                        <div className="flex flex-col ml-2">
                            <span className="font-semibold text-sm">
                                {id.row.original.Description}
                            </span>
                        </div>
                    </div>
                )
            },
        },
 */
        {
            header: 'Oluşturma',
            accessorKey: 'CreatedDate',
            cell: (id: any) => {
                const formattedDate = new Date(
                    id.row.original.CreatedDate
                ).toLocaleDateString('tr-TR')
                return (
                    <div className="flex items-center">
                        <div className="flex flex-col ml-2">
                            <span className="font-semibold text-sm">
                                {formattedDate}
                            </span>
                        </div>
                    </div>
                )
            },
        },

        {
            header: 'Güncellenme',
            accessorKey: 'UpdatedDate',
            cell: (id: any) => {
                const formattedDate = new Date(
                    id.row.original.UpdatedDate
                ).toLocaleDateString('tr-TR')
                return (
                    <div className="flex items-center">
                        <div className="flex flex-col ml-2">
                            <span className="font-semibold text-sm">
                                {formattedDate}
                            </span>
                        </div>
                    </div>
                )
            },
        },
        {
            header: 'Güncelleyen',
            accessorKey: 'UpdatedBy',
            cell: (id: any) => {
                return (
                    <div className="flex items-center">
                        <div className="flex flex-col ml-2">
                            <span className="font-semibold text-sm">
                                {id.row.original.UpdatedBy}
                            </span>
                        </div>
                    </div>
                )
            },
        },
        {
            header: 'Link',
            accessorKey: 'Id',
            cell: (id: any) => {
                return (
                    <div className="flex items-center gap-2">
                        {id.row.original?.Description && (
                            <Tooltip title={id.row.original?.Description}>
                                <CgInfo className="text-amber-500" size={20} />
                            </Tooltip>
                        )}
                        <Link
                            className="flex whitespace-nowrap text-blue-500 text-sm gap-2 items-center"
                            to={`/orders/${id.row.original.Id}`}
                        >
                            <CgExternal size={20} />
                            Detay Gör
                        </Link>
                    </div>
                )
            },
        },
    ]

    return (
        <DetailShadowContainer
            header="Vakalar"
            headerExtra={'Vaka Sayısı: ' + totalData}
        >
            {totalData > 0 ? (
                <TableGrid
                    totalData={totalData}
                    columns={DoctorOrdersColumns}
                    data={data}
                    removePaginationButtons={true}
                />
            ) : (
                <div className="flex my-8 justify-center items-center">
                    <span className="text-xl font-bold text-gray-400">
                        Vaka Bulunamadı
                    </span>
                </div>
            )}
            {totalData > 0 && (
                <div className="flex justify-between items-center mt-4">
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-500">
                            {`${pageIndex * pageSize + 1}-${Math.min((pageIndex + 1) * pageSize, totalData)} / ${totalData} kayıt`}
                        </span>
                      {/*  <Select
                            size="sm"
                            value={{ value: pageSize, label: `${pageSize} / sayfa` }}
                            options={[
                                { value: 10, label: '10 / sayfa' },
                                { value: 25, label: '25 / sayfa' },
                                { value: 50, label: '50 / sayfa' },
                                { value: 100, label: '100 / sayfa' },
                            ]}
                            onChange={(option: any) => handlePageSizeChange(option?.value)}
                            isSearchable={false}
                            className="w-32"
                        />*/}
                    </div>
                    <Pagination
                        currentPage={pageIndex + 1}
                        pageSize={pageSize}
                        total={totalData}
                        onChange={handlePaginationChange}
                        displayTotal={false}
                    />
                </div>
            )}
        </DetailShadowContainer>
    )
}

export default DoctorOrdersContainer
