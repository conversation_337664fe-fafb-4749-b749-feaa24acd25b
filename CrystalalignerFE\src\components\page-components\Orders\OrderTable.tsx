import { Order } from '@/@types/order'
import ExcelDownloadButton from '@/components/shared/ExcelDownloadButton'
import SearchBox from '@/components/shared/SearchBox'
import TableGrid from '@/components/shared/TableGrid'
import { Button } from '@/components/ui'
import {
    apiGetOrdersByAgentId,
    apiGetOrdersByDoctorId,
    apiGetOrdersPaged,
} from '@/services/OrderService'
import { apiGetPackageTypes } from '@/services/PackageService'
import { apiGetPatients } from '@/services/PatientService'
import {
    setFilterAll,
    setFilterChipValue,
    setFilterColumnField,
    setFilterValue,
    useAppDispatch,
    useAppSelector,
} from '@/store'
import { setTableProps } from '@/store/slices/table'
import { capitalize } from '@/utils/capitalizeFilter'
import { useCallback, useEffect, useState } from 'react'
import { Link } from 'react-router-dom'
import OrderColumns from './OrderColumns'
import OrderFilterDropdown from './OrderComponents/OrderFilterDropdown'
import OrderStatusTooltip from './OrderComponents/OrderStatusTooltip'
import AgentOrderSearchBox from '../Agents/AgentOrderSearchBox'
import { ORDER_STATUS_KEYS } from '@/components/ui/utils/constants'
import { Pagination } from 'antd'

const OrderTable = () => {
    const [orders, setOrders] = useState<Order[]>([])
    const [patients, setPatients] = useState<any[]>([])
    const [packageTypes, setPackageTypes] = useState<any[]>([])
    const [loading, setLoading] = useState<boolean>(true)
    const [totalData, setTotalData] = useState<number>(0)
    const [error, setError] = useState<string | null>(null)
    const [data, setData] = useState<Order[]>([])

    const user = useAppSelector((state) => state.auth.user)
    const userId = user?.Id
    const table = useAppSelector((state) => state.table.tableProps)
    const { pageSize = 100, pageIndex = 0 } = table
    const isAgent = user.UserType === 2 || user.UserType === 6

    const { filterValue, filterChipValue, sortField, sortOperator, filterAll } =
        useAppSelector((state) => state.filter)

    const dispatch = useAppDispatch()

    useEffect(() => {
        dispatch(
            setTableProps({
                pageIndex: 0,
                pageSize: 100,
                sorting: [],
            })
        )
    }, [])

    // Reset filters on component mount
    useEffect(() => {
        dispatch(setFilterChipValue(null))
        dispatch(setFilterColumnField(null))
        dispatch(setFilterValue(null))
    }, [dispatch])

    // Fetch patients and package types on mount
    useEffect(() => {
        const fetchPatients = async () => {
            try {
                setLoading(true)
                const response = await apiGetPatients()
                if ((response as any)?.data?.Success) {
                    setPatients((response as any).data.Data)
                } else {
                    setError('Failed to fetch patients')
                }
            } catch (e) {
                setError('An error occurred while fetching patients')
                console.error(e)
            } finally {
                setLoading(false)
            }
        }

        const fetchPackageTypes = async () => {
            try {
                const response = await apiGetPackageTypes()
                if ((response as any)?.data?.Success) {
                    setPackageTypes((response as any).data.Data)
                }
            } catch (e) {
                console.error(e)
            }
        }

        fetchPatients()
        fetchPackageTypes()
    }, [])

    const getOrders = useCallback(async () => {
        if (!patients.length) return
        setLoading(true)
        try {
            let response
            const currentPage = pageIndex + 1

            if (user.UserType === 1) {
                response = await apiGetOrdersPaged({
                    pageSize: pageSize,
                    page: currentPage,
                    columnField: filterChipValue ? 'Status' : 'Patient.Name',
                    filterValue: filterChipValue || filterValue,
                    filterOperator: filterChipValue ? 'equals' : 'contains',
                    sortOperator: sortOperator,
                    sortField: sortField && capitalize(sortField),
                })
            } else if (user.UserType === 3) {
                if (!userId) return
                response = await apiGetOrdersByDoctorId({
                    pageSize: pageSize,
                    page: currentPage,
                    columnField: filterChipValue
                        ? 'Status'
                        : filterValue
                        ? 'Patient.Name'
                        : 'CreatedId',
                    filterValue:
                        filterChipValue || filterValue || userId.toString(),
                    filterOperator: filterChipValue
                        ? 'equals'
                        : filterValue
                        ? 'contains'
                        : 'equals',
                    sortOperator: sortOperator,
                    sortField: sortField && capitalize(sortField),
                })
            } else if ([2, 6].includes(user.UserType as number)) {
                if (!userId) return

                response = await apiGetOrdersByAgentId({
                    pageSize: pageSize,
                    page: currentPage,
                    columnField: filterChipValue ? 'Status' : 'Patient.Name',
                    filterValue: filterChipValue || filterValue,
                    filterOperator: filterChipValue ? 'equals' : 'contains',
                    sortOperator: sortOperator,
                    sortField: sortField && capitalize(sortField),
                    agentId: userId,
                })
            } else {
                response = await apiGetOrdersPaged({
                    pageSize: pageSize,
                    page: currentPage,
                })
            }

            if ((response as any)?.data?.Success) {
                const responseData = (response as any).data.Data.map(
                    (order: Order) => ({
                        ...order,
                        Patient: patients.find((p) => p.Id === order.PatientId),
                    })
                )
                setOrders(responseData)
                setTotalData(
                    (response as any).data.TotalRecords || responseData.length
                )
            } else {
                setOrders([])
                setTotalData(0)
            }
        } catch (e) {
            console.error(e)
            setOrders([])
            setTotalData(0)
        } finally {
            setLoading(false)
        }
    }, [
        user.UserType,
        userId,
        patients,
        filterChipValue,
        filterValue,
        sortField,
        sortOperator,
        pageSize,
        pageIndex,
    ])

    // Fetch orders when dependencies change
    useEffect(() => {
        getOrders()
    }, [getOrders])

    // Update data when orders change
    useEffect(() => {
        setData(orders)
    }, [orders])

    // Handle agent order search
    const handleAgentOrderSearch = (filteredOrders: Order[]) => {
        setData(filteredOrders)
        dispatch(setFilterValue(''))
        dispatch(setFilterChipValue(null))
        dispatch(setFilterAll())
    }

    // Apply status filter to data for client-side filtering (removed isAgent condition)
    useEffect(() => {
        let filteredData = orders
        if (filterChipValue) {
            const statusIndex = ORDER_STATUS_KEYS.findIndex(
                (key) => key === filterChipValue
            )
            filteredData =
                statusIndex !== -1
                    ? filteredData.filter(
                          (order) => order.Status === statusIndex
                      )
                    : []
        }
        if (filterAll) {
            filteredData = orders
        }
        setData(filteredData)
    }, [orders, filterChipValue, filterAll])

    const handlePaginationChange = (page: number, size: number) => {
        dispatch(
            setTableProps({
                pageIndex: page - 1,
                pageSize: size,
                sorting: table.sorting || [],
            })
        )
    }

    const handlePageSizeChange = (current: number, size: number) => {
        dispatch(
            setTableProps({
                pageIndex: 0,
                pageSize: size,
                sorting: table.sorting || [],
            })
        )
    }

    return (
        <div>
            <div className="w-full flex flex-col lg:flex-row justify-between gap-4 lg:gap-0 lg:items-center mb-4">
                <h2>Vakalar</h2>
                <div className="flex gap-4 items-center">
                    <OrderStatusTooltip />
                    {user.UserType === 1 || user.UserType === 3 ? (
                        <div className="flex items-center gap-4">
                            <OrderFilterDropdown />
                            <SearchBox placeholder="Vaka Ara" />
                        </div>
                    ) : (
                        <div className="flex items-center gap-4">
                            <OrderFilterDropdown />
                            <AgentOrderSearchBox
                                patients={patients}
                                orders={orders}
                                placeholder="Vaka Ara"
                                onSearch={handleAgentOrderSearch}
                            />
                        </div>
                    )}
                    {(user.UserType === 3 || user.UserType === 1) && (
                        <Link to="/order-add">
                            <Button variant="twoTone" color="green">
                                Vaka Oluştur
                            </Button>
                        </Link>
                    )}
                    <ExcelDownloadButton
                        size="sm"
                        isOrderList={true}
                        orderStatus={filterChipValue}
                    />
                </div>
            </div>
            <TableGrid
                removePaginationButtons={true}
                columns={OrderColumns(
                    patients,
                    packageTypes,
                    pageSize,
                    pageIndex
                )}
                totalData={totalData}
                data={data}
                loading={loading}
                clientSorting={false}
                rowOnClick={true}
                navigateBaseURL="/orders"
            />
            <div className="flex justify-end mt-4">
                <Pagination
                    current={pageIndex + 1}
                    pageSize={pageSize}
                    total={totalData}
                    showSizeChanger={true}
                    showQuickJumper={true}
                    showTotal={(total, range) =>
                        `${range[0]}-${range[1]} / ${total} kayıt`
                    }
                    pageSizeOptions={['100']}
                    onChange={handlePaginationChange}
                    onShowSizeChange={handlePageSizeChange}
                    className="bg-white"
                    size="default"
                />
            </div>
        </div>
    )
}

export default OrderTable
